import os
import pickle
import time
from concurrent.futures import ThreadPoolExecutor
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from modules.base import TikTok

class Parser(TikTok):
    URL = 'https://www.tiktok.com/'
    LOGIN_URL = 'https://www.tiktok.com/login/phone-or-email/email'

    def __init__(self, email, password, proxy=None, headless=False):
        super().__init__(proxy, headless)
        self.email = email
        self.password = password
        self.cookies_path = os.path.join(os.path.abspath('data'), 'cookies')
        self.cookies_file_path = os.path.join(self.cookies_path, f'{self.email}.pkl')

        os.makedirs(self.cookies_path, exist_ok=True)

    def __input_keyword(self, keyword):
        print(f'Searching for keyword: {keyword}')

        # Find the input element using Selenium and input data into it
        search_input = self._wait_for_element_clickable(By.CSS_SELECTOR, 'input[data-e2e="search-user-input"]', 60)

        # Clear any existing text in the search input
        search_input.clear()
        time.sleep(1)

        # Input the keyword
        search_input.send_keys(keyword)
        time.sleep(2)  # Wait a moment for the input to register

        print(f'Entered keyword "{keyword}" into search box')

        # Press the Enter key
        search_input.send_keys(Keys.ENTER)

        print('Pressed Enter to search')
        time.sleep(3)  # Wait for search to initiate

    def __wait_for_content_load(self):
        print('Waiting for search results to load...')
        try:
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.XPATH, '//div[@data-e2e="search_video-item"]'))
            )
            print('Search results loaded successfully!')
        except Exception as e:
            print(f"Content loading took longer than expected. Error: {e}")
            print(f"Current URL: {self.driver.current_url}")
            # Check if we're on a search results page
            if 'search' in self.driver.current_url:
                print('We are on a search page, but video items not found yet. Continuing anyway...')
            else:
                print('Warning: Not on a search results page!')

    def __scroll_to_bottom(self):
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)  # Wait for content to load

    def __collect_links(self, selector):
        elements = self.driver.find_elements(By.XPATH, selector)
        return set(element.get_attribute('href') for element in elements if element.get_attribute('href'))

    def __parsing_processing(self, selector, existing_links, max_links=2000, max_attempts=50):
        self.__wait_for_content_load()
        video_links = set()
        attempts = 0
        last_height = self.driver.execute_script("return document.body.scrollHeight")

        while len(video_links) < max_links and attempts < max_attempts:
            self.__scroll_to_bottom()
            new_links = self.__collect_links(selector)
            video_links.update(new_links - existing_links)

            print(f'Found new videos: {len(video_links)}')

            new_height = self.driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                attempts += 1
            else:
                attempts = 0
                last_height = new_height

            if attempts >= 5:
                print("No new content loaded after multiple scrolls. Breaking the loop.")
                break

        print(f"Total links collected: {len(video_links)}")
        return list(video_links)[:max_links]

    def __read_existing_links(self, file_path):
        existing_links = set()
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as file:
                existing_links = set(line.strip() for line in file)
        return existing_links

    def __save_new_links(self, file_path, new_links):
        with open(file_path, 'a', encoding='utf-8') as file:
            for link in new_links:
                file.write(link + '\n')

    def parse_by_keyword(self, key, mode='top', max_links=2000):
        save_path = os.path.join(self.results_path, key)
        os.makedirs(save_path, exist_ok=True)

        file_path = os.path.join(save_path, f'{key}.txt')
        existing_links = self.__read_existing_links(file_path)

        # Check if we're already on the TikTok homepage, if not navigate there
        current_url = self.driver.current_url
        if not current_url.startswith('https://www.tiktok.com'):
            print('Not on TikTok homepage, navigating there...')
            self.driver.get(self.URL)
            time.sleep(3)
        else:
            print('Already on TikTok homepage, proceeding with search...')

        self.__input_keyword(key)
        self.__wait_for_content_load()

        selector = '//div[@data-e2e="search_top-item"]//a[@tabindex="-1"]' if mode == 'top' else '//div[@data-e2e="search_video-item"]//a[@tabindex="-1"]'

        new_video_links = self.__parsing_processing(selector, existing_links, max_links)

        self.__save_new_links(file_path, new_video_links)
        print(f'Total new unique links added: {len(new_video_links)}')

    def login(self):
        # Check if cookies file exists
        if self.__cookies_file_exists(self.cookies_file_path):
            print('Cookies found. Logging into the account with the cookies...')

            # Load cookies from the existing file
            with open(self.cookies_file_path, "rb") as cookies_file:
                cookies = pickle.load(cookies_file)

            # Navigate to the login URL and add cookies to the current session
            self.driver.get(self.LOGIN_URL)
            for cookie in cookies:
                try:
                    self.driver.add_cookie(cookie)
                except Exception as e:
                    print(f"Failed to add cookie: {e}")

            # Navigate to the main TikTok page to verify login
            print('Navigating to TikTok homepage to verify login...')
            self.driver.get(self.URL)

            # Wait a moment for the page to load
            time.sleep(5)

            # Check if we're successfully logged in by looking for login-specific elements
            # If we see login elements, the cookie login failed
            try:
                # Check if we're still on a login page or redirected to login
                current_url = self.driver.current_url
                if 'login' in current_url.lower():
                    print('Cookie login failed - still on login page. Attempting manual login...')
                    self.__manual_login()
                else:
                    # Additional check: look for user-specific elements that indicate successful login
                    try:
                        # Look for the search input which should be available when logged in
                        self._wait_for_element_located(By.CSS_SELECTOR, 'input[data-e2e="search-user-input"]', timeout=10)
                        print('Cookie login successful!')
                    except:
                        print('Cookie login may have failed - search input not found. Attempting manual login...')
                        self.__manual_login()
            except Exception as e:
                print(f'Error verifying cookie login: {e}. Attempting manual login...')
                self.__manual_login()
        else:
            print('No cookies found. Performing manual login...')
            self.__manual_login()

    def __manual_login(self):
        try:
            print('Logging into the account manually...')

            # Navigate to the login URL
            self.driver.get(self.LOGIN_URL)

            # Find and fill in the email input field
            input_email = self._wait_for_element_located(By.CSS_SELECTOR, 'input[type="text"]')
            input_email.send_keys(self.email)

            # Find and fill in the password input field
            input_password = self._wait_for_element_located(By.CSS_SELECTOR, 'input[type="password"]')
            input_password.send_keys(self.password)

            # Find and click the login button
            login_button = self.driver.find_element(By.CSS_SELECTOR, 'button[data-e2e="login-button"]')
            login_button.click()

            time.sleep(30)  # Use time.sleep() instead of just sleep()

            # Wait for the login button to become invisible (indicating successful login)
            self._wait_for_element_invisible(By.CSS_SELECTOR, 'button[data-e2e="login-button"]')

            # Navigate to main page after successful login
            self.driver.get(self.URL)
            time.sleep(3)

            # Get the current session cookies and save them to a file
            cookies = self.driver.get_cookies()
            with open(self.cookies_file_path, "wb") as cookies_file:
                pickle.dump(cookies, cookies_file)

            print('Manual login successful and cookies saved!')
        except Exception as e:
            # Raise an exception in case of a login error
            raise Exception("Login error: \n", e)

    @staticmethod
    def __cookies_file_exists(cookies_filename):
        return os.path.exists(cookies_filename)

    def parse_multiple_keywords(self, keywords):
        self.login()
        for key in keywords:
            print(f"Parsing keyword: {key}")
            self.parse_by_keyword(key, max_links=2000)
            print(f"Finished parsing keyword: {key}")

        # Close the browser after all keywords are processed
        self.driver.quit()
